package http

import (
	"log/slog"
	"net/http"
	"strconv"
	"strings"
	"time"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"github.com/gin-gonic/gin"
)

// CreateCourse godoc
//
//			@Summary		CreateCourse
//			@Description	create new course
//			@Description
//			@Description	Field Constraints:
//			@Description	- courseType: Must be one of 'IIT-JEE', 'NEET' (required)
//			@Description	- name: Must be unique across all courses (required)
//			@Description	- isFree: Boolean flag indicating if course is free (defaults to false)
//			@Description	- subjectNames: Array of existing subject names to associate with the course (optional)
//	     @Security       BearerAuth
//		 @Param			item	body	models.CourseForCreate	true	"course details"
//			@Tags			courses
//			@Accept			json
//			@Produce		json
//			@Success		200	{object}	models.SimpleEntityResponse
//			@Failure		400	{object}	HTTPError
//			@Failure		404	{object}	HTTPError
//			@Failure		500	{object}	HTTPError
//			@Router			/courses [post]
func (h *Handlers) CreateCourse(ctx *gin.Context) {
	courseInput := new(models.CourseForCreate)
	if err := ctx.ShouldBindJSON(courseInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate course type before creating
	if courseInput.CourseType != models.CourseTypeIITJEE && courseInput.CourseType != models.CourseTypeNEET {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid course type. Must be either 'IIT-JEE' or 'NEET'",
		})
		return
	}

	course := &models.Course{
		Name:           courseInput.Name,
		Description:    courseInput.Description,
		Price:          courseInput.Price,
		Discount:       courseInput.Discount,
		DurationInDays: courseInput.DurationInDays,
		IsFree:         courseInput.IsFree,
		CourseType:     courseInput.CourseType,
	}

	createdCourse, err := h.db.CreateCourse(ctx.Request.Context(), course, courseInput.SubjectNames)
	if err != nil {
		// Check for specific error types
		if strings.Contains(err.Error(), "subjects not found:") {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only ID and name
	response := models.SimpleEntityResponse{
		ID:   createdCourse.ID,
		Name: createdCourse.Name,
	}
	ctx.JSON(http.StatusOK, response)
}

// GetCourses godoc
//
//		@Summary		Get Courses
//		@Description	get courses for the logged in user. Returns all courses for non-student users (admin, etc.) and filtered courses for students (free courses + enrolled paid courses)
//	 @Security       BearerAuth
//		@Tags			courses
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.CoursesByCategory
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/courses [get]
//
// GetCourses is the HTTP handler to get list of courses
// For students: returns free courses and paid courses they are enrolled in
// For non-students (admin, etc.): returns all courses
func (h *Handlers) GetCourses(ctx *gin.Context) {
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	courses, err := h.db.GetCourses(ctx.Request.Context(),
		userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, courses)
}

// AssociateTestWithCourse godoc
//
//	@Summary		Associate Test with Course
//	@Description	associate an existing test with an existing course
//	@Security       BearerAuth
//	@Param			course_id	path	int	true	"Course ID"
//	@Param			test_id		path	int	true	"Test ID"
//	@Tags			courses
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	map[string]interface{}
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		409	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/courses/{course_id}/tests/{test_id} [post]
func (h *Handlers) AssociateTestWithCourse(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	// Extract course ID from URL parameter
	courseIDStr := ctx.Param("course_id")
	courseID, err := strconv.ParseUint(courseIDStr, 10, 32)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("AssociateTestWithCourse failed - invalid course ID",
			"client_ip", clientIP,
			"course_id_param", courseIDStr,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Extract test ID from URL parameter
	testIDStr := ctx.Param("test_id")
	testID, err := strconv.ParseUint(testIDStr, 10, 32)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("AssociateTestWithCourse failed - invalid test ID",
			"client_ip", clientIP,
			"course_id", courseID,
			"test_id_param", testIDStr,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid test ID"})
		return
	}

	slog.Info("Associating test with course",
		"client_ip", clientIP,
		"course_id", courseID,
		"test_id", testID,
	)

	// Call the database method to associate test with course
	err = h.db.AssociateTestWithCourse(ctx.Request.Context(), uint(courseID), uint(testID))
	if err != nil {
		duration := time.Since(start)

		// Check for specific error types
		if err.Error() == "course with ID "+courseIDStr+" not found" ||
			err.Error() == "test with ID "+testIDStr+" not found" {
			slog.Warn("AssociateTestWithCourse failed - resource not found",
				"client_ip", clientIP,
				"course_id", courseID,
				"test_id", testID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "test with ID "+testIDStr+" is already associated with course ID "+courseIDStr {
			slog.Warn("AssociateTestWithCourse failed - already associated",
				"client_ip", clientIP,
				"course_id", courseID,
				"test_id", testID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusConflict, gin.H{"error": err.Error()})
			return
		}

		slog.Error("AssociateTestWithCourse failed - database error",
			"client_ip", clientIP,
			"course_id", courseID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("Test associated with course successfully",
		"client_ip", clientIP,
		"course_id", courseID,
		"test_id", testID,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{
		"message":   "Test successfully associated with course",
		"course_id": courseID,
		"test_id":   testID,
	})
}
