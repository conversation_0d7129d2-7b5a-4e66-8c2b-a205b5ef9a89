package http

import (
	"fmt"
	"log/slog"
	"net/http"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
)

// CreateTopic godoc
//
//			@Summary		CreateTopic
//			@Description	create new topic for questions
//	     @Security       BearerAuth
//		 @Param			item	body	models.TopicForCreate	true	"topic details"
//			@Tags			questions
//			@Accept			json
//			@Produce		json
//			@Success		200	{object}	models.SimpleEntityResponse
//			@Failure		400	{object}	HTTPError
//			@Failure		404	{object}	HTTPError
//			@Failure		500	{object}	HTTPError
//			@Router			/topics [post]
func (h *Handlers) CreateTopic(ctx *gin.Context) {
	topicInput := new(models.TopicForCreate)
	if err := ctx.ShouldBindJSON(topicInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	topic := &models.Topic{
		Name: topicInput.Name,
	}

	createdTopic, err := h.db.CreateTopic(ctx.Request.Context(), topic, topicInput.ChapterName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only ID and name
	response := models.SimpleEntityResponse{
		ID:   createdTopic.ID,
		Name: createdTopic.Name,
	}
	ctx.JSON(http.StatusOK, response)
}

// GetTopics godoc
//
//	@Summary		Get Topics
//	@Description	get topics for a chapter_name, or all topics if no chapter_name provided
//	@Security       BearerAuth
//	@Param			chapter_name	query		string	false	"Chapter Name (optional)"
//	@Tags			questions
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.Topic
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/topics [get]
func (h *Handlers) GetTopics(ctx *gin.Context) {
	chapterName := ctx.Query("chapter_name")

	topics, err := h.db.GetTopics(ctx.Request.Context(), chapterName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, topics)
}

// CreateQuestion godoc
//
//			@Summary		CreateQuestion
//			@Description	create new question
//			@Description
//			@Description	Field Constraints:
//			@Description	- text: Question text is required
//			@Description	- topicName: Must reference an existing topic (required)
//			@Description	- difficultyName: Must reference an existing difficulty level (required)
//			@Description	- questionType: Type of question (e.g., 'MCQ', 'text')
//			@Description	- correctAnswer: Only used for text questions, not for MCQ questions
//			@Description	- options: Required for MCQ questions, should contain option details
//	     @Security       BearerAuth
//		 @Param			item	body	models.QuestionForCreate	true	"question details"
//			@Tags			questions
//			@Accept			json
//			@Produce		json
//			@Success		200	{object}	models.SimpleEntityResponse
//			@Failure		400	{object}	HTTPError
//			@Failure		404	{object}	HTTPError
//			@Failure		500	{object}	HTTPError
//			@Router			/questions [post]
func (h *Handlers) CreateQuestion(ctx *gin.Context) {
	questionInput := new(models.QuestionForCreate)
	if err := ctx.ShouldBindJSON(questionInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate question type and related fields
	if err := validateQuestionInput(questionInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	question := &models.Question{
		Text:          questionInput.Text,
		QuestionType:  questionInput.QuestionType,
		ImageUrl:      questionInput.ImageUrl,
		FileUrl:       questionInput.FileUrl,
		CorrectAnswer: questionInput.CorrectAnswer,
	}

	createdQuestion, err := h.db.CreateQuestion(ctx.Request.Context(), question, questionInput.TopicName, questionInput.DifficultyName, questionInput.Options)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only ID and name (using Text as name for questions)
	response := models.SimpleEntityResponse{
		ID:   createdQuestion.ID,
		Name: createdQuestion.Text,
	}
	ctx.JSON(http.StatusOK, response)
}

// validateQuestionInput validates the question input based on question type
func validateQuestionInput(input *models.QuestionForCreate) error {
	switch input.QuestionType {
	case "mcq", "multi-select":
		// For MCQ questions, options are required and CorrectAnswer should be empty
		if len(input.Options) == 0 {
			return fmt.Errorf("MCQ questions must have options")
		}

		if input.CorrectAnswer != "" {
			return fmt.Errorf("MCQ questions should not have CorrectAnswer field set - use IsCorrect in options instead")
		}

		// Count correct options
		correctCount := 0
		for _, option := range input.Options {
			if option.IsCorrect {
				correctCount++
			}
		}

		if input.QuestionType == "mcq" && correctCount != 1 {
			return fmt.Errorf("MCQ questions must have exactly one correct option, found %d", correctCount)
		}

		if input.QuestionType == "multi-select" && correctCount == 0 {
			return fmt.Errorf("multi-select questions must have at least one correct option")
		}

	case "text":
		// For text questions, CorrectAnswer is required and options should be empty
		if input.CorrectAnswer == "" {
			return fmt.Errorf("text questions must have CorrectAnswer field set")
		}

		if len(input.Options) > 0 {
			return fmt.Errorf("text questions should not have options")
		}

	default:
		return fmt.Errorf("invalid question type: %s. Must be 'mcq', 'multi-select', or 'text'", input.QuestionType)
	}

	return nil
}

// GetQuestions godoc
//
//	@Summary		Get Questions
//	@Description	get questions with optional filters for subject, chapter, topic, and difficulty
//	@Security       BearerAuth
//	@Param			subject		query		string	false	"Subject name (optional)"
//	@Param			chapter		query		string	false	"Chapter name (optional)"
//	@Param			topic		query		string	false	"Topic name (optional)"
//	@Param			difficulty	query		string	false	"Difficulty level (optional)"
//	@Tags			questions
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.Question
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/questions [get]
//
// GetQuestions is the HTTP handler to get list of questions with optional filters
func (h *Handlers) GetQuestions(ctx *gin.Context) {
	// Get optional query parameters
	subjectName := ctx.Query("subject")
	chapterName := ctx.Query("chapter")
	topicName := ctx.Query("topic")
	difficulty := ctx.Query("difficulty")

	// Log the request with filters
	slog.Info("GetQuestions request",
		"subject", subjectName,
		"chapter", chapterName,
		"topic", topicName,
		"difficulty", difficulty,
		"client_ip", ctx.ClientIP(),
	)

	questions, err := h.db.GetQuestions(ctx.Request.Context(), subjectName, chapterName, topicName, difficulty)
	if err != nil {
		slog.Error("GetQuestions failed",
			"subject", subjectName,
			"chapter", chapterName,
			"topic", topicName,
			"difficulty", difficulty,
			"error", err.Error(),
			"client_ip", ctx.ClientIP(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	slog.Info("GetQuestions successful",
		"subject", subjectName,
		"chapter", chapterName,
		"topic", topicName,
		"difficulty", difficulty,
		"question_count", len(questions),
		"client_ip", ctx.ClientIP(),
	)

	ctx.JSON(http.StatusOK, gin.H{"questions": questions})
}
