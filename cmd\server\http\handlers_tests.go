package http

import (
	"log/slog"
	"net/http"
	"strconv"
	"time"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"github.com/gin-gonic/gin"
)

// CreateSectionType godoc
//
//	@Summary		CreateSectionType
//	@Description	create new section type
//	@Description
//	@Description	Field Constraints:
//	@Description	- name: Section type name must be unique (required)
//	@Description	- subjectName: Must reference an existing subject (required)
//	@Security       BearerAuth
//	@Param			item	body	models.SectionTypeForCreate	true	"section type details"
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.SimpleEntityResponse
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/section-types [post]
func (h *Handlers) CreateSectionType(ctx *gin.Context) {
	sectionTypeInput := new(models.SectionTypeForCreate)
	if err := ctx.ShouldBindJSON(sectionTypeInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	sectionType := &models.SectionType{
		Name:          sectionTypeInput.Name,
		QuestionCount: sectionTypeInput.QuestionCount,
		PositiveMarks: sectionTypeInput.PositiveMarks,
		NegativeMarks: sectionTypeInput.NegativeMarks,
	}

	createdSectionType, err := h.db.CreateSectionType(ctx.Request.Context(), sectionType, sectionTypeInput.SubjectName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only ID and name
	response := models.SimpleEntityResponse{
		ID:   createdSectionType.ID,
		Name: createdSectionType.Name,
	}
	ctx.JSON(http.StatusOK, response)
}

// CreateTestType godoc
//
//	@Summary		CreateTestType
//	@Description	create new test type
//	@Description
//	@Description	Field Constraints:
//	@Description	- name: Test type name must be unique (required)
//	@Security       BearerAuth
//	@Param			item	body	models.TestTypeForCreate	true	"test type details"
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.SimpleEntityResponse
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/test-types [post]
func (h *Handlers) CreateTestType(ctx *gin.Context) {
	testTypeInput := new(models.TestTypeForCreate)
	if err := ctx.ShouldBindJSON(testTypeInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	testType := &models.TestType{
		Name: testTypeInput.Name,
	}

	createdTestType, err := h.db.CreateTestType(ctx.Request.Context(), testType, testTypeInput.SectionTypeNames)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only ID and name
	response := models.SimpleEntityResponse{
		ID:   createdTestType.ID,
		Name: createdTestType.Name,
	}
	ctx.JSON(http.StatusOK, response)
}

// CreateTest godoc
//
//	@Summary		CreateTest
//	@Description	create new test of a given type
//	@Description
//	@Description	Field Constraints:
//	@Description	- name: Test name is required
//	@Description	- testTypeName: Must reference an existing test type (required)
//	@Description	- fromTime: Start time for the test (required)
//	@Description	- toTime: End time for the test (required)
//	@Description	- sections: Array of sections for the test
//	@Security       BearerAuth
//	@Param			item	body	models.TestForCreate	true	"test details"
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.SimpleEntityResponse
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/tests [post]
func (h *Handlers) CreateTest(ctx *gin.Context) {
	testInput := new(models.TestForCreate)
	if err := ctx.ShouldBindJSON(testInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	test := &models.Test{
		Name:        testInput.Name,
		FromTime:    testInput.FromTime,
		ToTime:      testInput.ToTime,
		Active:      true, // Default to active
		Description: testInput.Description,
	}

	createdTest, err := h.db.CreateTest(ctx.Request.Context(), test, testInput.TestTypeName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only ID and name
	response := models.SimpleEntityResponse{
		ID:   createdTest.ID,
		Name: createdTest.Name,
	}
	ctx.JSON(http.StatusOK, response)
}

// AddQuestionsToTest godoc
//
//	@Summary		AddQuestionsToTest
//	@Description	add questions to a test
//	@Security       BearerAuth
//	@Param			test_id	path	int	true	"Test ID"
//	@Param			item	body	models.AddQuestionsToTestRequest	true	"question IDs and section name"
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	map[string]string
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/tests/{test_id}/questions [post]
func (h *Handlers) AddQuestionsToTest(ctx *gin.Context) {
	testIDStr := ctx.Param("test_id")
	testID, err := strconv.ParseUint(testIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid test ID"})
		return
	}

	var request models.AddQuestionsToTestRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if len(request.QuestionIDs) == 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "No question IDs provided"})
		return
	}

	if request.SectionName == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Section name is required"})
		return
	}

	err = h.db.AddQuestionsToTest(ctx.Request.Context(), uint(testID), request.QuestionIDs, request.SectionName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Questions added to test successfully"})
}

// RemoveQuestionsFromTest godoc
//
//	@Summary		RemoveQuestionsFromTest
//	@Description	remove questions from a test
//	@Security       BearerAuth
//	@Param			test_id	path	int	true	"Test ID"
//	@Param			item	body	models.RemoveQuestionsFromTestRequest	true	"question IDs and section name"
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	map[string]string
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/tests/{test_id}/questions [delete]
func (h *Handlers) RemoveQuestionsFromTest(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("RemoveQuestionsFromTest request started",
		"client_ip", clientIP,
		"method", ctx.Request.Method,
		"path", ctx.Request.URL.Path,
	)

	// Parse test ID from URL parameter
	testIDStr := ctx.Param("test_id")
	testID, err := strconv.ParseUint(testIDStr, 10, 32)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("RemoveQuestionsFromTest failed - invalid test ID",
			"client_ip", clientIP,
			"test_id", testIDStr,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid test ID"})
		return
	}

	// Parse request body
	var request models.RemoveQuestionsFromTestRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		duration := time.Since(start)
		slog.Warn("RemoveQuestionsFromTest failed - invalid request body",
			"client_ip", clientIP,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate request data
	if len(request.QuestionIDs) == 0 {
		duration := time.Since(start)
		slog.Warn("RemoveQuestionsFromTest failed - no question IDs provided",
			"client_ip", clientIP,
			"test_id", testID,
			"section_name", request.SectionName,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "No question IDs provided"})
		return
	}

	if request.SectionName == "" {
		duration := time.Since(start)
		slog.Warn("RemoveQuestionsFromTest failed - section name is required",
			"client_ip", clientIP,
			"test_id", testID,
			"question_ids", request.QuestionIDs,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Section name is required"})
		return
	}

	// Remove questions from test
	err = h.db.RemoveQuestionsFromTest(ctx.Request.Context(), uint(testID), request.QuestionIDs, request.SectionName)
	if err != nil {
		duration := time.Since(start)
		slog.Error("RemoveQuestionsFromTest failed - database error",
			"client_ip", clientIP,
			"test_id", testID,
			"section_name", request.SectionName,
			"question_ids", request.QuestionIDs,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("RemoveQuestionsFromTest completed successfully",
		"client_ip", clientIP,
		"test_id", testID,
		"section_name", request.SectionName,
		"question_ids", request.QuestionIDs,
		"question_count", len(request.QuestionIDs),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"message": "Questions removed from test successfully"})
}

// GetTests godoc
//
//	@Summary		Get Tests
//	@Description	get tests for the logged in user. Students see tests from enrolled courses and all ZSAT type tests. Admins see all tests.
//	@Security       BearerAuth
//	@Param			active	query	bool	false	"Filter by active status (true/false)"
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.TestForGet
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/tests [get]
func (h *Handlers) GetTests(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	// Extract user ID from JWT token
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("GetTests failed - token extraction error",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse active filter parameter (optional)
	var activeOnly *bool
	activeParam := ctx.Query("active")
	if activeParam != "" {
		if activeParam == "true" {
			active := true
			activeOnly = &active
		} else if activeParam == "false" {
			active := false
			activeOnly = &active
		} else {
			duration := time.Since(start)
			slog.Warn("GetTests failed - invalid active parameter",
				"user_id", userID,
				"client_ip", clientIP,
				"active_param", activeParam,
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid active parameter. Use 'true' or 'false'"})
			return
		}
	}

	slog.Debug("GetTests request",
		"user_id", userID,
		"client_ip", clientIP,
		"active_filter", activeParam,
	)

	// Get tests from database with appropriate filtering
	tests, err := h.db.GetTests(ctx.Request.Context(), userID, activeOnly)
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetTests failed - database error",
			"user_id", userID,
			"client_ip", clientIP,
			"active_filter", activeParam,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("GetTests successful",
		"user_id", userID,
		"client_ip", clientIP,
		"active_filter", activeParam,
		"test_count", len(tests),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"tests": tests})
}

// GetTestTypes godoc
//
//	@Summary		Get Test Types
//	@Description	get all test types with their associated section types
//	@Security       BearerAuth
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.TestType
//	@Failure		400	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/test-types [get]
func (h *Handlers) GetTestTypes(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("GetTestTypes request started",
		"client_ip", clientIP,
		"method", ctx.Request.Method,
		"path", ctx.Request.URL.Path,
	)

	// Get test types from database
	testTypes, err := h.db.GetTestTypes(ctx.Request.Context())
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetTestTypes failed - database error",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("GetTestTypes completed successfully",
		"client_ip", clientIP,
		"test_type_count", len(testTypes),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, testTypes)
}

// GetSectionTypes godoc
//
//	@Summary		Get Section Types
//	@Description	get all section types with their associated subjects
//	@Security       BearerAuth
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.SectionType
//	@Failure		400	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/section-types [get]
func (h *Handlers) GetSectionTypes(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("GetSectionTypes request started",
		"client_ip", clientIP,
		"method", ctx.Request.Method,
		"path", ctx.Request.URL.Path,
	)

	// Get section types from database
	sectionTypes, err := h.db.GetSectionTypes(ctx.Request.Context())
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetSectionTypes failed - database error",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("GetSectionTypes completed successfully",
		"client_ip", clientIP,
		"section_type_count", len(sectionTypes),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, sectionTypes)
}

// ToggleTestActiveStatus godoc
//
//	@Summary		Toggle Test Active Status
//	@Description	toggle active status of a test
//	@Security       BearerAuth
//	@Param			test_id	path	int	true	"Test ID"
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	map[string]string
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/tests/{test_id}/active [put]
func (h *Handlers) ToggleTestActiveStatus(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	// Extract test ID from URL parameter
	testIDStr := ctx.Param("test_id")
	testID, err := strconv.ParseUint(testIDStr, 10, 32)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("ToggleTestActiveStatus failed - invalid test ID",
			"client_ip", clientIP,
			"test_id_param", testIDStr,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid test ID"})
		return
	}

	// Toggle active status of test
	err = h.db.ToggleTestActiveStatus(ctx.Request.Context(), uint(testID))
	if err != nil {
		duration := time.Since(start)
		slog.Error("ToggleTestActiveStatus failed - database error",
			"client_ip", clientIP,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("ToggleTestActiveStatus successful",
		"client_ip", clientIP,
		"test_id", testID,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"message": "Test active status toggled successfully"})
}
