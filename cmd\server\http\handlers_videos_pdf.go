package http

import (
	"log/slog"
	"net/http"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
)

// GetContent godoc
//
//	@Summary		Get Content
//	@Description	get videos and study materials as flat lists sorted by updated date
//	@Security       BearerAuth
//	@Param			subject_name	query		string	false	"Subject Name (optional - if not provided, returns content for all subjects)"
//	@Tags			content
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.Content
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/content [get]
func (h *Handlers) GetContent(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	subjectName := ctx.Query("subject_name")

	slog.Info("GetContent request started",
		"client_ip", clientIP,
		"subject_name", subjectName,
	)

	content, err := h.db.GetContent(ctx.Request.Context(), subjectName)
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetContent failed - database error",
			"client_ip", clientIP,
			"subject_name", subjectName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("GetContent successful",
		"client_ip", clientIP,
		"subject_name", subjectName,
		"video_count", len(content.Videos),
		"material_count", len(content.Pdfs),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, content)
}

// AddVideo godoc
//
//		@Summary	    AddVideo
//		@Description	add a new video
//	     @Security       BearerAuth
//	 @Param			item	body	models.VideoForCreate	true	"video details"
//		@Tags			library
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.SimpleEntityResponse
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/videos [post]
func (h *Handlers) AddVideo(ctx *gin.Context) {
	videoInput := new(models.VideoForCreate)
	if err := ctx.ShouldBindJSON(videoInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	video := &models.Video{
		Name:        videoInput.Name,
		DisplayName: videoInput.DisplayName,
		VideoUrl:    videoInput.VideoUrl,
		ViewCount:   0, // Default to 0
	}

	createdVideo, err := h.db.AddVideo(ctx.Request.Context(), video, videoInput.ChapterName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only ID and name
	response := models.SimpleEntityResponse{
		ID:   createdVideo.ID,
		Name: createdVideo.Name,
	}
	ctx.JSON(http.StatusOK, response)
}

// AddStudyMaterial godoc
//
//		@Summary	    AddStudyMaterial
//		@Description	add a new material
//	     @Security       BearerAuth
//	 @Param			item	body	models.MaterialForCreate	true	"material details"
//		@Tags			library
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.SimpleEntityResponse
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/studymaterials [post]
//
// AddStudyMaterial is the HTTP handler to add a new pdf to a chapter
func (h *Handlers) AddStudyMaterial(ctx *gin.Context) {
	materialInput := new(models.MaterialForCreate)
	if err := ctx.ShouldBindJSON(materialInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	material := &models.StudyMaterial{
		Name:        materialInput.Name,
		DisplayName: materialInput.DisplayName,
		Url:         materialInput.Url,
	}

	createdMaterial, err := h.db.AddStudyMaterial(ctx.Request.Context(), material, materialInput.ChapterName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only ID and name
	response := models.SimpleEntityResponse{
		ID:   createdMaterial.ID,
		Name: createdMaterial.Name,
	}
	ctx.JSON(http.StatusOK, response)
}
