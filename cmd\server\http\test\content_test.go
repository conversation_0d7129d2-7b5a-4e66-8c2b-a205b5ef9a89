package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestGetContentAPI(t *testing.T) {
	// Skip if noAuthRouter is not initialized
	if noAuthRouter == nil {
		t.Skip("NoAuthRouter not initialized - skipping content API test")
		return
	}

	// Clean up before test
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	testSubjects := []string{
		"Physics Content Test " + timestamp,
		"Chemistry Content Test " + timestamp,
		"Mathematics Content Test " + timestamp,
	}

	// Clean up any existing test data
	for _, subjectName := range testSubjects {
		db.Exec("DELETE FROM videos WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM study_materials WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
	}

	// Create test subjects
	physicsSubject := models.Subject{
		Name:        testSubjects[0],
		DisplayName: "Physics Content Test Display",
	}
	db.Create(&physicsSubject)

	chemistrySubject := models.Subject{
		Name:        testSubjects[1],
		DisplayName: "Chemistry Content Test Display",
	}
	db.Create(&chemistrySubject)

	mathSubject := models.Subject{
		Name:        testSubjects[2],
		DisplayName: "Mathematics Content Test Display",
	}
	db.Create(&mathSubject)

	// Create test chapters
	physicsChapter1 := models.Chapter{
		Name:        "Mechanics " + timestamp,
		DisplayName: "Mechanics Display",
		SubjectID:   physicsSubject.ID,
	}
	db.Create(&physicsChapter1)

	physicsChapter2 := models.Chapter{
		Name:        "Thermodynamics " + timestamp,
		DisplayName: "Thermodynamics Display",
		SubjectID:   physicsSubject.ID,
	}
	db.Create(&physicsChapter2)

	chemistryChapter := models.Chapter{
		Name:        "Organic Chemistry " + timestamp,
		DisplayName: "Organic Chemistry Display",
		SubjectID:   chemistrySubject.ID,
	}
	db.Create(&chemistryChapter)

	mathChapter := models.Chapter{
		Name:        "Calculus " + timestamp,
		DisplayName: "Calculus Display",
		SubjectID:   mathSubject.ID,
	}
	db.Create(&mathChapter)

	// Create test videos
	physicsVideo1 := models.Video{
		Name:        "mechanics_intro_" + timestamp,
		DisplayName: "Introduction to Mechanics",
		VideoUrl:    "https://example.com/mechanics_intro.mp4",
		ViewCount:   150,
		ChapterID:   physicsChapter1.ID,
	}
	db.Create(&physicsVideo1)

	physicsVideo2 := models.Video{
		Name:        "thermodynamics_basics_" + timestamp,
		DisplayName: "Thermodynamics Basics",
		VideoUrl:    "https://example.com/thermodynamics_basics.mp4",
		ViewCount:   200,
		ChapterID:   physicsChapter2.ID,
	}
	db.Create(&physicsVideo2)

	chemistryVideo := models.Video{
		Name:        "organic_reactions_" + timestamp,
		DisplayName: "Organic Reactions",
		VideoUrl:    "https://example.com/organic_reactions.mp4",
		ViewCount:   100,
		ChapterID:   chemistryChapter.ID,
	}
	db.Create(&chemistryVideo)

	// Create test study materials
	physicsMaterial := models.StudyMaterial{
		Name:        "mechanics_notes_" + timestamp,
		DisplayName: "Mechanics Study Notes",
		Url:         "https://example.com/mechanics_notes.pdf",
		ChapterID:   physicsChapter1.ID,
	}
	db.Create(&physicsMaterial)

	chemistryMaterial := models.StudyMaterial{
		Name:        "organic_notes_" + timestamp,
		DisplayName: "Organic Chemistry Notes",
		Url:         "https://example.com/organic_notes.pdf",
		ChapterID:   chemistryChapter.ID,
	}
	db.Create(&chemistryMaterial)

	mathMaterial := models.StudyMaterial{
		Name:        "calculus_notes_" + timestamp,
		DisplayName: "Calculus Study Notes",
		Url:         "https://example.com/calculus_notes.pdf",
		ChapterID:   mathChapter.ID,
	}
	db.Create(&mathMaterial)

	// Test 1: Get all content (no subject filter)
	t.Run("GetAllContent", func(t *testing.T) {
		resp := requestExecutionHelper(http.MethodGet, "/api/content", nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var content models.Content
		err := json.Unmarshal(resp.Body.Bytes(), &content)
		assert.Nil(t, err)

		// Should return flat lists of videos and materials
		assert.GreaterOrEqual(t, len(content.Videos), 3, "Should return at least our 3 test videos")
		assert.GreaterOrEqual(t, len(content.Pdfs), 3, "Should return at least our 3 test materials")

		// Verify videos are sorted by updated_at (most recent first)
		// Since we created them in sequence, they should be in reverse order
		videoNames := make([]string, len(content.Videos))
		for i, video := range content.Videos {
			videoNames[i] = video.Name
		}

		// Verify materials are sorted by updated_at (most recent first)
		materialNames := make([]string, len(content.Pdfs))
		for i, material := range content.Pdfs {
			materialNames[i] = material.Name
		}

		// Verify our test videos are present
		foundVideos := 0
		for _, video := range content.Videos {
			if video.Name == "mechanics_intro_"+timestamp ||
				video.Name == "thermodynamics_basics_"+timestamp ||
				video.Name == "organic_reactions_"+timestamp {
				foundVideos++
			}
		}
		assert.Equal(t, 3, foundVideos, "Should find all 3 test videos")

		// Verify our test materials are present
		foundMaterials := 0
		for _, material := range content.Pdfs {
			if material.Name == "mechanics_notes_"+timestamp ||
				material.Name == "organic_notes_"+timestamp ||
				material.Name == "calculus_notes_"+timestamp {
				foundMaterials++
			}
		}
		assert.Equal(t, 3, foundMaterials, "Should find all 3 test materials")
	})

	// Test 2: Get content for specific subject (Physics)
	t.Run("GetPhysicsContent", func(t *testing.T) {
		url := fmt.Sprintf("/api/content?subject_name=%s", testSubjects[0])
		resp := requestExecutionHelper(http.MethodGet, url, nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var content models.Content
		err := json.Unmarshal(resp.Body.Bytes(), &content)
		assert.Nil(t, err)

		// Should return only Physics content (2 videos, 1 material)
		assert.Equal(t, 2, len(content.Videos), "Physics should have 2 videos")
		assert.Equal(t, 1, len(content.Pdfs), "Physics should have 1 study material")

		// Verify videos are from Physics subject
		videoNames := make(map[string]bool)
		for _, video := range content.Videos {
			videoNames[video.Name] = true
		}
		assert.True(t, videoNames["mechanics_intro_"+timestamp], "Should contain mechanics video")
		assert.True(t, videoNames["thermodynamics_basics_"+timestamp], "Should contain thermodynamics video")

		// Verify study material is from Physics subject
		materialNames := make(map[string]bool)
		for _, material := range content.Pdfs {
			materialNames[material.Name] = true
		}
		assert.True(t, materialNames["mechanics_notes_"+timestamp], "Should contain mechanics notes")

		// Verify video structure (using VideoForGet)
		var mechanicsVideo *models.VideoForGet
		for _, video := range content.Videos {
			if video.Name == "mechanics_intro_"+timestamp {
				mechanicsVideo = &video
				break
			}
		}
		assert.NotNil(t, mechanicsVideo, "Should find mechanics video")
		assert.Equal(t, "Introduction to Mechanics", mechanicsVideo.DisplayName)
		assert.Equal(t, "https://example.com/mechanics_intro.mp4", mechanicsVideo.VideoUrl)
		assert.Equal(t, uint(150), mechanicsVideo.ViewCount)
		assert.Equal(t, physicsChapter1.ID, mechanicsVideo.ChapterID)

		// Verify study material structure (using StudyMaterialForGet)
		var mechanicsMaterial *models.StudyMaterialForGet
		for _, material := range content.Pdfs {
			if material.Name == "mechanics_notes_"+timestamp {
				mechanicsMaterial = &material
				break
			}
		}
		assert.NotNil(t, mechanicsMaterial, "Should find mechanics material")
		assert.Equal(t, "Mechanics Study Notes", mechanicsMaterial.DisplayName)
		assert.Equal(t, "https://example.com/mechanics_notes.pdf", mechanicsMaterial.Url)
		assert.Equal(t, physicsChapter1.ID, mechanicsMaterial.ChapterID)
	})

	// Test 3: Get content for non-existent subject
	t.Run("GetNonExistentSubjectContent", func(t *testing.T) {
		url := "/api/content?subject_name=NonExistentSubject"
		resp := requestExecutionHelper(http.MethodGet, url, nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var content models.Content
		err := json.Unmarshal(resp.Body.Bytes(), &content)
		assert.Nil(t, err)

		// Should return empty lists for non-existent subject
		assert.Equal(t, 0, len(content.Videos), "Should return empty videos list for non-existent subject")
		assert.Equal(t, 0, len(content.Pdfs), "Should return empty materials list for non-existent subject")
	})

	// Test 4: Get content for Chemistry subject
	t.Run("GetChemistryContent", func(t *testing.T) {
		url := fmt.Sprintf("/api/content?subject_name=%s", testSubjects[1])
		resp := requestExecutionHelper(http.MethodGet, url, nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var content models.Content
		err := json.Unmarshal(resp.Body.Bytes(), &content)
		assert.Nil(t, err)

		// Should return only Chemistry content (1 video, 1 material)
		assert.Equal(t, 1, len(content.Videos), "Chemistry should have 1 video")
		assert.Equal(t, 1, len(content.Pdfs), "Chemistry should have 1 study material")

		// Verify video is from Chemistry subject
		video := content.Videos[0]
		assert.Equal(t, "organic_reactions_"+timestamp, video.Name)
		assert.Equal(t, "Organic Reactions", video.DisplayName)
		assert.Equal(t, "https://example.com/organic_reactions.mp4", video.VideoUrl)
		assert.Equal(t, uint(100), video.ViewCount)
		assert.Equal(t, chemistryChapter.ID, video.ChapterID)

		// Verify study material is from Chemistry subject
		material := content.Pdfs[0]
		assert.Equal(t, "organic_notes_"+timestamp, material.Name)
		assert.Equal(t, "Organic Chemistry Notes", material.DisplayName)
		assert.Equal(t, "https://example.com/organic_notes.pdf", material.Url)
		assert.Equal(t, chemistryChapter.ID, material.ChapterID)
	})

	// Cleanup
	for _, subjectName := range testSubjects {
		db.Exec("DELETE FROM videos WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM study_materials WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
	}
}
