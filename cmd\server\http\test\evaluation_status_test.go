package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestEvaluationStatusFlow(t *testing.T) {
	timestamp := strconv.FormatInt(time.Now().UnixNano(), 10)

	// Step 1: Create test infrastructure
	subject := models.SubjectForCreate{
		Name:        fmt.Sprintf("EvalSubject_%s", timestamp),
		DisplayName: fmt.Sprintf("EvalSubject_%s", timestamp),
	}
	subjectResp := requestExecutionHelper(http.MethodPost, "/api/subjects", subject)
	assert.Equal(t, http.StatusOK, subjectResp.Code)

	sectionType := models.SectionTypeForCreate{
		Name:        fmt.Sprintf("EvalSectionType_%s", timestamp),
		SubjectName: subject.Name,
	}
	sectionTypeResp := requestExecutionHelper(http.MethodPost, "/api/section-types", sectionType)
	assert.Equal(t, http.StatusOK, sectionTypeResp.Code)

	testType := models.TestTypeForCreate{
		Name:             fmt.Sprintf("EvalTestType_%s", timestamp),
		SectionTypeNames: []string{sectionType.Name},
	}
	testTypeResp := requestExecutionHelper(http.MethodPost, "/api/test-types", testType)
	assert.Equal(t, http.StatusOK, testTypeResp.Code)

	// Step 2: Create a test
	test := models.TestForCreate{
		Name:         fmt.Sprintf("EvalTest_%s", timestamp),
		TestTypeName: testType.Name,
		Description:  "Test for evaluation status flow",
	}
	testResp := requestExecutionHelper(http.MethodPost, "/api/tests", test)
	assert.Equal(t, http.StatusOK, testResp.Code)

	var createdTestResponse models.TestResponse
	err := json.Unmarshal(testResp.Body.Bytes(), &createdTestResponse)
	assert.Nil(t, err)

	// Step 3: Verify initial evaluation status is NotApplicable
	var createdTest models.Test
	err = db.First(&createdTest, createdTestResponse.ID).Error
	assert.Nil(t, err)
	assert.Equal(t, models.EvaluationStatusNotApplicable, createdTest.EvaluationStatus, "Test should start with NotApplicable status")

	// Step 4: Test the UpdateTestEvaluationStatus function directly
	// Test updating to Pending status
	err = server.UpdateTestEvaluationStatus(nil, createdTestResponse.ID, models.EvaluationStatusPending)
	assert.Nil(t, err, "Should be able to update evaluation status to Pending")

	// Verify status changed to Pending
	err = db.First(&createdTest, createdTestResponse.ID).Error
	assert.Nil(t, err)
	assert.Equal(t, models.EvaluationStatusPending, createdTest.EvaluationStatus, "Test should have Pending status after update")

	// Step 5: Test updating to Evaluated status
	err = server.UpdateTestEvaluationStatus(nil, createdTestResponse.ID, models.EvaluationStatusEvaluated)
	assert.Nil(t, err, "Should be able to update evaluation status to Evaluated")

	// Verify status changed to Evaluated
	err = db.First(&createdTest, createdTestResponse.ID).Error
	assert.Nil(t, err)
	assert.Equal(t, models.EvaluationStatusEvaluated, createdTest.EvaluationStatus, "Test should have Evaluated status after update")

	// Step 6: Test the DetermineTestEvaluationStatus function
	status, err := server.DetermineTestEvaluationStatus(nil, createdTestResponse.ID)
	assert.Nil(t, err, "Should be able to determine evaluation status")
	assert.Equal(t, models.EvaluationStatusNotApplicable, status, "Test with no responses should have NotApplicable status")

	// Cleanup
	db.Exec("DELETE FROM sections WHERE test_id = ?", createdTestResponse.ID)
	db.Exec("DELETE FROM tests WHERE id = ?", createdTestResponse.ID)
	db.Exec("DELETE FROM test_types WHERE name = ?", testType.Name)
	db.Exec("DELETE FROM section_types WHERE name = ?", sectionType.Name)
	db.Exec("DELETE FROM subjects WHERE name = ?", subject.Name)
}
