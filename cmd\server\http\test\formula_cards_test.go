package test

import (
	"encoding/json"
	"net/http"
	"net/url"
	"testing"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestCreateFormulaCards(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", "Physics Test Formula")
	db.Exec("DELETE FROM subjects WHERE name = ?", "Physics Test Formula")

	// First create a subject to associate formula cards with
	newSubject := models.SubjectForCreate{
		Name:        "Physics Test Formula",
		DisplayName: "Physics",
	}

	subjectRR := requestExecutionHelper(http.MethodPost, "/api/subjects", newSubject)
	assert.Equal(t, http.StatusOK, subjectRR.Code)

	var createdSubject models.Subject
	err := json.Unmarshal(subjectRR.Body.Bytes(), &createdSubject)
	assert.Nil(t, err)

	// Now create formula cards for this subject
	formulaCardsInput := models.FormulaCardsForCreate{
		SubjectName: "Physics Test Formula",
		FormulaCards: []models.FormulaCardForCreate{
			{
				Name:     "Newton's Second Law",
				ImageUrl: "https://example.com/newton-second-law.png",
			},
			{
				Name:     "Kinetic Energy Formula",
				ImageUrl: "https://example.com/kinetic-energy.png",
			},
		},
	}

	rr := requestExecutionHelper(http.MethodPost, "/api/formula-cards", formulaCardsInput)

	// Check status code
	assert.Equal(t, http.StatusOK, rr.Code)

	// Parse response (now returns array of SimpleEntityResponse)
	var response []models.SimpleEntityResponse
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	assert.Nil(t, err)

	assert.Len(t, response, 2)
	assert.Equal(t, "Newton's Second Law", response[0].Name)
	assert.NotZero(t, response[0].ID)

	// Verify they were persisted in the database
	var fromDB []models.FormulaCard
	err = db.Where("subject_id = ?", createdSubject.ID).Find(&fromDB).Error
	assert.Nil(t, err)
	assert.Len(t, fromDB, 2)

	// Verify the first card details from database
	assert.Equal(t, "Newton's Second Law", fromDB[0].Name)
	assert.Equal(t, "https://example.com/newton-second-law.png", fromDB[0].ImageUrl)
	assert.Equal(t, createdSubject.ID, fromDB[0].SubjectID)
}

func TestGetFormulaCardsBySubject(t *testing.T) {
	// Clean up before test - clean up all test subjects
	testSubjects := []string{"Mathematics Test Formula", "Physics Test Formula", "Chemistry Test Formula"}
	for _, subjectName := range testSubjects {
		db.Exec("DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
	}

	// Create multiple subjects with their formula cards
	subjects := []struct {
		name        string
		displayName string
		cards       []models.FormulaCardForCreate
	}{
		{
			name:        "Mathematics Test Formula",
			displayName: "Mathematics",
			cards: []models.FormulaCardForCreate{
				{
					Name:     "Quadratic Formula",
					ImageUrl: "https://example.com/quadratic-formula.png",
				},
				{
					Name:     "Pythagorean Theorem",
					ImageUrl: "https://example.com/pythagorean-theorem.png",
				},
			},
		},
		{
			name:        "Physics Test Formula",
			displayName: "Physics",
			cards: []models.FormulaCardForCreate{
				{
					Name:     "Newton's Second Law",
					ImageUrl: "https://example.com/newton-second-law.png",
				},
				{
					Name:     "Kinetic Energy Formula",
					ImageUrl: "https://example.com/kinetic-energy.png",
				},
				{
					Name:     "Ohm's Law",
					ImageUrl: "https://example.com/ohms-law.png",
				},
			},
		},
		{
			name:        "Chemistry Test Formula",
			displayName: "Chemistry",
			cards: []models.FormulaCardForCreate{
				{
					Name:     "Ideal Gas Law",
					ImageUrl: "https://example.com/ideal-gas-law.png",
				},
			},
		},
	}

	// Create subjects and their formula cards
	for _, subject := range subjects {
		// Create subject
		newSubject := models.SubjectForCreate{
			Name:        subject.name,
			DisplayName: subject.displayName,
		}

		subjectRR := requestExecutionHelper(http.MethodPost, "/api/subjects", newSubject)
		assert.Equal(t, http.StatusOK, subjectRR.Code)

		// Create formula cards for this subject
		formulaCardsInput := models.FormulaCardsForCreate{
			SubjectName:  subject.name,
			FormulaCards: subject.cards,
		}

		createRR := requestExecutionHelper(http.MethodPost, "/api/formula-cards", formulaCardsInput)
		assert.Equal(t, http.StatusOK, createRR.Code)
	}

	// Test getting all formula cards organized by subject (new behavior)
	getRR := requestExecutionHelper(http.MethodGet, "/api/formula-cards", nil)
	assert.Equal(t, http.StatusOK, getRR.Code)

	// Parse response
	var response map[string][]models.FormulaCardsBySubject
	err := json.Unmarshal(getRR.Body.Bytes(), &response)
	assert.Nil(t, err)

	organizedCards := response["formula_cards_by_subject"]
	assert.Len(t, organizedCards, 3) // Should have 3 subjects

	// Create a map for easier verification
	subjectMap := make(map[string]models.FormulaCardsBySubject)
	for _, subjectCards := range organizedCards {
		subjectMap[subjectCards.SubjectName] = subjectCards
	}

	// Verify each subject has the correct cards
	for _, expectedSubject := range subjects {
		actualSubject, exists := subjectMap[expectedSubject.name]
		assert.True(t, exists, "Subject %s should exist in response", expectedSubject.name)
		assert.Len(t, actualSubject.FormulaCards, len(expectedSubject.cards), "Subject %s should have correct number of cards", expectedSubject.name)

		// Verify each card has the required 4 attributes
		for _, card := range actualSubject.FormulaCards {
			assert.NotZero(t, card.ID)
			assert.NotEmpty(t, card.Name)
			assert.Equal(t, expectedSubject.name, card.SubjectName)
			assert.NotEmpty(t, card.ImageUrl)
		}
	}

	// Test that subject_name parameter is ignored (endpoint always returns all subjects)
	t.Run("Verify subject_name parameter is ignored", func(t *testing.T) {
		// Try to get specific subject - should still return all subjects organized
		getURL := "/api/formula-cards?subject_name=" + url.QueryEscape("Mathematics Test Formula")
		getRR := requestExecutionHelper(http.MethodGet, getURL, nil)

		assert.Equal(t, http.StatusOK, getRR.Code)

		var response map[string][]models.FormulaCardsBySubject
		err := json.Unmarshal(getRR.Body.Bytes(), &response)
		assert.Nil(t, err)

		organizedCards := response["formula_cards_by_subject"]
		assert.Len(t, organizedCards, 3) // Should still return all 3 subjects, ignoring the parameter
	})

	// Clean up
	for _, subjectName := range testSubjects {
		db.Exec("DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
	}
}

func TestCreateFormulaCardsInvalidSubject(t *testing.T) {
	// Try to create formula cards for a non-existent subject
	formulaCardsInput := models.FormulaCardsForCreate{
		SubjectName: "NonExistentSubject",
		FormulaCards: []models.FormulaCardForCreate{
			{
				Name:     "Test Formula",
				ImageUrl: "https://example.com/test.png",
			},
		},
	}

	rr := requestExecutionHelper(http.MethodPost, "/api/formula-cards", formulaCardsInput)

	// Should return 404 for non-existent subject
	assert.Equal(t, http.StatusNotFound, rr.Code)

	var response map[string]string
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, "Subject not found", response["error"])
}

func TestGetFormulaCardsBySubjectEmpty(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", "Biology Test Formula")
	db.Exec("DELETE FROM subjects WHERE name = ?", "Biology Test Formula")

	// Create a subject without formula cards
	newSubject := models.SubjectForCreate{
		Name:        "Biology Test Formula",
		DisplayName: "Biology",
	}

	subjectRR := requestExecutionHelper(http.MethodPost, "/api/subjects", newSubject)
	assert.Equal(t, http.StatusOK, subjectRR.Code)

	// Get all formula cards (should include the new subject but with no cards)
	getRR := requestExecutionHelper(http.MethodGet, "/api/formula-cards", nil)

	// Check status code
	assert.Equal(t, http.StatusOK, getRR.Code)

	// Parse response
	var response map[string][]models.FormulaCardsBySubject
	err := json.Unmarshal(getRR.Body.Bytes(), &response)
	assert.Nil(t, err)

	organizedCards := response["formula_cards_by_subject"]

	// Find the Biology subject and verify it has no formula cards
	var biologySubject *models.FormulaCardsBySubject
	for _, subjectCards := range organizedCards {
		if subjectCards.SubjectName == "Biology Test Formula" {
			biologySubject = &subjectCards
			break
		}
	}

	// Biology subject should exist but have no formula cards
	if biologySubject != nil {
		assert.Len(t, biologySubject.FormulaCards, 0)
	}
}

func TestGetAllFormulaCardsOrganizedBySubject(t *testing.T) {
	// Clean up before test - remove all existing formula cards and subjects to ensure clean state
	db.Exec("DELETE FROM formula_cards")
	db.Exec("DELETE FROM subjects WHERE name LIKE '%Test%' OR name LIKE '%Formula%' OR name LIKE '%Organized%'")

	testSubjects := []string{"Physics Organized Test", "Chemistry Organized Test", "Math Organized Test"}
	for _, subject := range testSubjects {
		db.Exec("DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subject)
		db.Exec("DELETE FROM subjects WHERE name = ?", subject)
	}

	// Create test subjects and formula cards
	subjectData := []struct {
		name  string
		cards []models.FormulaCardForCreate
	}{
		{
			name: "Physics Organized Test",
			cards: []models.FormulaCardForCreate{
				{Name: "Newton's Laws", ImageUrl: "https://example.com/newton.png"},
				{Name: "Energy Conservation", ImageUrl: "https://example.com/energy.png"},
			},
		},
		{
			name: "Chemistry Organized Test",
			cards: []models.FormulaCardForCreate{
				{Name: "Periodic Table", ImageUrl: "https://example.com/periodic.png"},
			},
		},
		{
			name: "Math Organized Test",
			cards: []models.FormulaCardForCreate{
				{Name: "Quadratic Formula", ImageUrl: "https://example.com/quadratic.png"},
				{Name: "Calculus Basics", ImageUrl: "https://example.com/calculus.png"},
				{Name: "Trigonometry", ImageUrl: "https://example.com/trig.png"},
			},
		},
	}

	// Create subjects and their formula cards
	for _, subjectInfo := range subjectData {
		// Create subject
		newSubject := models.SubjectForCreate{
			Name:        subjectInfo.name,
			DisplayName: subjectInfo.name,
		}
		subjectRR := requestExecutionHelper(http.MethodPost, "/api/subjects", newSubject)
		assert.Equal(t, http.StatusOK, subjectRR.Code)

		// Create formula cards for this subject
		formulaCardsInput := models.FormulaCardsForCreate{
			SubjectName:  subjectInfo.name,
			FormulaCards: subjectInfo.cards,
		}
		cardsRR := requestExecutionHelper(http.MethodPost, "/api/formula-cards", formulaCardsInput)
		assert.Equal(t, http.StatusOK, cardsRR.Code)
	}

	// Test getting all formula cards organized by subject (no subject_name parameter)
	getRR := requestExecutionHelper(http.MethodGet, "/api/formula-cards", nil)
	assert.Equal(t, http.StatusOK, getRR.Code)

	// Parse response
	var response map[string][]models.FormulaCardsBySubject
	err := json.Unmarshal(getRR.Body.Bytes(), &response)
	assert.Nil(t, err)

	organizedCards := response["formula_cards_by_subject"]
	assert.Len(t, organizedCards, 3) // Should have 3 subjects

	// Verify the structure and content
	subjectMap := make(map[string]models.FormulaCardsBySubject)
	for _, subjectCards := range organizedCards {
		subjectMap[subjectCards.SubjectName] = subjectCards
	}

	// Verify Physics cards
	physicsCards, exists := subjectMap["Physics Organized Test"]
	assert.True(t, exists)
	assert.Len(t, physicsCards.FormulaCards, 2)

	// Verify each card has the required 4 attributes
	for _, card := range physicsCards.FormulaCards {
		assert.NotZero(t, card.ID)
		assert.NotEmpty(t, card.Name)
		assert.Equal(t, "Physics Organized Test", card.SubjectName)
		assert.NotEmpty(t, card.ImageUrl)
	}

	// Verify Chemistry cards
	chemistryCards, exists := subjectMap["Chemistry Organized Test"]
	assert.True(t, exists)
	assert.Len(t, chemistryCards.FormulaCards, 1)
	assert.Equal(t, "Periodic Table", chemistryCards.FormulaCards[0].Name)

	// Verify Math cards
	mathCards, exists := subjectMap["Math Organized Test"]
	assert.True(t, exists)
	assert.Len(t, mathCards.FormulaCards, 3)

	// Test that the endpoint ignores subject_name parameter and always returns organized data
	ignoredParamRR := requestExecutionHelper(http.MethodGet, "/api/formula-cards?subject_name=Physics+Organized+Test", nil)
	assert.Equal(t, http.StatusOK, ignoredParamRR.Code)

	var ignoredParamResponse map[string][]models.FormulaCardsBySubject
	err = json.Unmarshal(ignoredParamRR.Body.Bytes(), &ignoredParamResponse)
	assert.Nil(t, err)

	ignoredParamCards := ignoredParamResponse["formula_cards_by_subject"]
	assert.Len(t, ignoredParamCards, 3) // Should still return all 3 subjects, ignoring the parameter

	// Clean up - restore clean state
	db.Exec("DELETE FROM formula_cards")
	for _, subject := range testSubjects {
		db.Exec("DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subject)
		db.Exec("DELETE FROM subjects WHERE name = ?", subject)
	}
}
