package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestGetStudentsAPI(t *testing.T) {
	// Skip if noAuthRouter is not initialized
	if noAuthRouter == nil {
		t.Skip("NoAuthRouter not initialized - skipping students API test")
		return
	}

	// Clean up before test
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	testEmails := []string{
		"student5_" + timestamp + "@test.com",
		"student6_" + timestamp + "@test.com",
		"student7_" + timestamp + "@test.com",
		"student8_" + timestamp + "@test.com",
	}

	// Clean up any existing test data
	for _, email := range testEmails {
		db.Exec("DELETE FROM students WHERE user_id IN (SELECT id FROM users WHERE email = ?)", email)
		db.Exec("DELETE FROM users WHERE email = ?", email)
	}

	// Create test students with different attributes
	student1 := models.Student{
		User: models.User{
			FullName:    "<PERSON> Johnson " + timestamp,
			Email:       testEmails[0],
			PhoneNumber: "9876543210",
			Role:        "Student",
		},
		ParentPhone: "9876543211",
		ParentEmail: "parent1_" + timestamp + "@test.com",
		Institute:   "ABC School",
		Class:       "11th",
		Stream:      "IIT-JEE",
		CityOrTown:  "Mumbai",
		State:       "Maharashtra",
	}
	db.Create(&student1)

	student2 := models.Student{
		User: models.User{
			FullName:    "Bob Smith " + timestamp,
			Email:       testEmails[1],
			PhoneNumber: "98765432155",
			Role:        "Student",
		},
		ParentPhone: "9876543213",
		ParentEmail: "parent2_" + timestamp + "@test.com",
		Institute:   "XYZ College",
		Class:       "12th",
		Stream:      "NEET",
		CityOrTown:  "Delhi",
		State:       "Delhi",
	}
	db.Create(&student2)

	student3 := models.Student{
		User: models.User{
			FullName:    "Charlie Brown " + timestamp,
			Email:       testEmails[2],
			PhoneNumber: "98765432144",
			Role:        "Student",
		},
		ParentPhone: "9876543215",
		ParentEmail: "parent3_" + timestamp + "@test.com",
		Institute:   "PQR Institute",
		Class:       "dropper",
		Stream:      "IIT-JEE",
		CityOrTown:  "Bangalore",
		State:       "Karnataka",
	}
	db.Create(&student3)

	student4 := models.Student{
		User: models.User{
			FullName:    "Diana Prince " + timestamp,
			Email:       testEmails[3],
			PhoneNumber: "9876543216",
			Role:        "Student",
		},
		ParentPhone: "9876543217",
		ParentEmail: "parent4_" + timestamp + "@test.com",
		Institute:   "LMN Academy",
		Class:       "11th",
		Stream:      "NEET",
		CityOrTown:  "Chennai",
		State:       "Tamil Nadu",
	}
	db.Create(&student4)

	// Test 1: Get all students (no filters)
	t.Run("GetAllStudents", func(t *testing.T) {
		resp := requestExecutionHelper(http.MethodGet, "/api/students", nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var students []models.StudentForList
		err := json.Unmarshal(resp.Body.Bytes(), &students)
		assert.Nil(t, err)

		// Should return at least our 4 test students
		assert.GreaterOrEqual(t, len(students), 4, "Should return at least our 4 test students")

		// Find our test students in the response
		foundStudents := 0
		for _, student := range students {
			for _, testEmail := range testEmails {
				if student.Email == testEmail {
					foundStudents++
					// Verify student structure
					assert.NotEmpty(t, student.FullName)
					assert.NotEmpty(t, student.Email)
					assert.NotEmpty(t, student.PhoneNumber)
					assert.NotEmpty(t, student.Class)
					assert.NotEmpty(t, student.Stream)
					break
				}
			}
		}
		assert.Equal(t, 4, foundStudents, "Should find all 4 test students")
	})

	// Test 2: Filter by stream (IIT-JEE)
	t.Run("FilterByStreamIITJEE", func(t *testing.T) {
		resp := requestExecutionHelper(http.MethodGet, "/api/students?stream=IIT-JEE", nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var students []models.StudentForList
		err := json.Unmarshal(resp.Body.Bytes(), &students)
		assert.Nil(t, err)

		// Find our IIT-JEE test students
		foundIITJEEStudents := 0
		for _, student := range students {
			if student.Email == testEmails[0] || student.Email == testEmails[2] {
				foundIITJEEStudents++
				assert.Equal(t, "IIT-JEE", student.Stream)
			}
		}
		assert.Equal(t, 2, foundIITJEEStudents, "Should find 2 IIT-JEE students")
	})

	// Test 3: Filter by stream (NEET)
	t.Run("FilterByStreamNEET", func(t *testing.T) {
		resp := requestExecutionHelper(http.MethodGet, "/api/students?stream=NEET", nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var students []models.StudentForList
		err := json.Unmarshal(resp.Body.Bytes(), &students)
		assert.Nil(t, err)

		// Find our NEET test students
		foundNEETStudents := 0
		for _, student := range students {
			if student.Email == testEmails[1] || student.Email == testEmails[3] {
				foundNEETStudents++
				assert.Equal(t, "NEET", student.Stream)
			}
		}
		assert.Equal(t, 2, foundNEETStudents, "Should find 2 NEET students")
	})

	// Test 4: Filter by class (11th)
	t.Run("FilterByClass11th", func(t *testing.T) {
		resp := requestExecutionHelper(http.MethodGet, "/api/students?class=11th", nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var students []models.StudentForList
		err := json.Unmarshal(resp.Body.Bytes(), &students)
		assert.Nil(t, err)

		// Find our 11th class test students
		found11thStudents := 0
		for _, student := range students {
			if student.Email == testEmails[0] || student.Email == testEmails[3] {
				found11thStudents++
				assert.Equal(t, "11th", student.Class)
			}
		}
		assert.Equal(t, 2, found11thStudents, "Should find 2 students in 11th class")
	})

	// Test 5: Filter by name prefix
	t.Run("FilterByNamePrefix", func(t *testing.T) {
		resp := requestExecutionHelper(http.MethodGet, "/api/students?name=Alice", nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var students []models.StudentForList
		err := json.Unmarshal(resp.Body.Bytes(), &students)
		assert.Nil(t, err)

		// Should find Alice
		foundAlice := false
		for _, student := range students {
			if student.Email == testEmails[0] {
				foundAlice = true
				assert.Contains(t, student.FullName, "Alice")
				break
			}
		}
		assert.True(t, foundAlice, "Should find Alice by name prefix")
	})

	// Test 6: Filter by email prefix match
	t.Run("FilterByEmailPrefix", func(t *testing.T) {
		emailPrefix := "student1_" + timestamp
		url := fmt.Sprintf("/api/students?email=%s", emailPrefix)
		resp := requestExecutionHelper(http.MethodGet, url, nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var students []models.StudentForList
		err := json.Unmarshal(resp.Body.Bytes(), &students)
		assert.Nil(t, err)

		// Should find student1 by email prefix
		foundStudent1 := false
		for _, student := range students {
			if student.Email == testEmails[0] {
				foundStudent1 = true
				assert.True(t, strings.HasPrefix(student.Email, emailPrefix), "Email should start with the prefix")
				break
			}
		}
		assert.True(t, foundStudent1, "Should find student1 by email prefix match")
	})

	// Test 7: Filter by phone number prefix
	t.Run("FilterByPhonePrefix", func(t *testing.T) {
		resp := requestExecutionHelper(http.MethodGet, "/api/students?phone_number=987654321", nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var students []models.StudentForList
		err := json.Unmarshal(resp.Body.Bytes(), &students)
		assert.Nil(t, err)

		// Should find all our test students (they all start with 987654321)
		foundTestStudents := 0
		for _, student := range students {
			for _, testEmail := range testEmails {
				if student.Email == testEmail {
					foundTestStudents++
					break
				}
			}
		}
		assert.Equal(t, 4, foundTestStudents, "Should find all 4 test students by phone prefix")
	})

	// Test 8: Filter by institution exact match
	t.Run("FilterByInstitution", func(t *testing.T) {
		resp := requestExecutionHelper(http.MethodGet, "/api/students?institution=ABC School", nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var students []models.StudentForList
		err := json.Unmarshal(resp.Body.Bytes(), &students)
		assert.Nil(t, err)

		// Should find Alice (who goes to ABC School)
		foundAlice := false
		for _, student := range students {
			if student.Email == testEmails[0] {
				foundAlice = true
				assert.Equal(t, "ABC School", student.Institute, "Should find student with exact institution match")
				break
			}
		}
		assert.True(t, foundAlice, "Should find Alice by institution exact match")
	})

	// Test 9: Combined filters (stream + class)
	t.Run("CombinedFilters", func(t *testing.T) {
		resp := requestExecutionHelper(http.MethodGet, "/api/students?stream=IIT-JEE&class=11th", nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var students []models.StudentForList
		err := json.Unmarshal(resp.Body.Bytes(), &students)
		assert.Nil(t, err)

		// Should find Alice (IIT-JEE + 11th)
		foundAlice := false
		for _, student := range students {
			if student.Email == testEmails[0] {
				foundAlice = true
				assert.Equal(t, "IIT-JEE", student.Stream)
				assert.Equal(t, "11th", student.Class)
				break
			}
		}
		assert.True(t, foundAlice, "Should find Alice with combined filters")
	})

	// Test 10: Combined filters (institution + stream)
	t.Run("CombinedFiltersInstitutionStream", func(t *testing.T) {
		resp := requestExecutionHelper(http.MethodGet, "/api/students?institution=ABC School&stream=IIT-JEE", nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var students []models.StudentForList
		err := json.Unmarshal(resp.Body.Bytes(), &students)
		assert.Nil(t, err)

		// Should find Alice (ABC School + IIT-JEE)
		foundAlice := false
		for _, student := range students {
			if student.Email == testEmails[0] {
				foundAlice = true
				assert.Equal(t, "ABC School", student.Institute)
				assert.Equal(t, "IIT-JEE", student.Stream)
				break
			}
		}
		assert.True(t, foundAlice, "Should find Alice with combined institution and stream filters")
	})

	// Test 11: Invalid stream parameter
	t.Run("InvalidStreamParameter", func(t *testing.T) {
		resp := requestExecutionHelper(http.MethodGet, "/api/students?stream=INVALID", nil)
		assert.Equal(t, http.StatusBadRequest, resp.Code)

		var response map[string]string
		err := json.Unmarshal(resp.Body.Bytes(), &response)
		assert.Nil(t, err)
		assert.Contains(t, response["error"], "Invalid stream parameter")
	})

	// Test 12: Invalid class parameter
	t.Run("InvalidClassParameter", func(t *testing.T) {
		resp := requestExecutionHelper(http.MethodGet, "/api/students?class=INVALID", nil)
		assert.Equal(t, http.StatusBadRequest, resp.Code)

		var response map[string]string
		err := json.Unmarshal(resp.Body.Bytes(), &response)
		assert.Nil(t, err)
		assert.Contains(t, response["error"], "Invalid class parameter")
	})

	// Cleanup
	for _, email := range testEmails {
		db.Exec("DELETE FROM students WHERE user_id IN (SELECT id FROM users WHERE email = ?)", email)
		db.Exec("DELETE FROM users WHERE email = ?", email)
	}
}
