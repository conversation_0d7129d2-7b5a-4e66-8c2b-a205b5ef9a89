package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func requestExecutionHelper(method, url string, payload interface{}) *httptest.ResponseRecorder {
	var req *http.Request
	if payload != nil {
		body, _ := json.Marshal(payload)
		req, _ = http.NewRequest(method, url, bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
	} else {
		req, _ = http.NewRequest(method, url, nil)
	}

	rr := httptest.NewRecorder()
	// Use noAuthRouter for tests to bypass authentication
	if noAuthRouter != nil {
		noAuthRouter.ServeHTTP(rr, req)
	} else {
		router.ServeHTTP(rr, req)
	}
	return rr
}

func requestExecutionHelperWithSpecificRouter(specificRouter *gin.Engine, method, url string, payload interface{}) *httptest.ResponseRecorder {
	var req *http.Request
	if payload != nil {
		body, _ := json.Marshal(payload)
		req, _ = http.NewRequest(method, url, bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
	} else {
		req, _ = http.NewRequest(method, url, nil)
	}

	rr := httptest.NewRecorder()
	specificRouter.ServeHTTP(rr, req)
	return rr
}

func authenticatedRequestHelper(method, url string, payload interface{}, token string) *httptest.ResponseRecorder {
	body, _ := json.Marshal(payload)
	req, _ := http.NewRequest(method, url, bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	rr := httptest.NewRecorder()
	// Use the authenticated router for these requests
	router.ServeHTTP(rr, req)
	return rr
}

func AddCoursesWithContent(t *testing.T, token string) models.Course {
	videoData := []models.Video{
		{
			Name:        "Go Basics Video",
			DisplayName: "Intro to Go Programming Video",
			VideoUrl:    "http://example.com/video1",
		},
	}
	studyMaterialData := []models.StudyMaterial{

		{
			Name:        "Go Basics PDF",
			DisplayName: "Intro to Go Programming Material",
			Url:         "http://example.com/study-material1",
		},
	}
	chapterData := []models.Chapter{
		{
			Name:           "Chapter 1",
			DisplayName:    "Introduction to Go",
			Videos:         videoData,
			StudyMaterials: studyMaterialData,
		},
	}
	subjects := []models.Subject{
		{Name: "Introduction", DisplayName: "Intro to Go", Chapters: chapterData},
		{Name: "Advanced Topics", DisplayName: "Go Concurrency"},
	}
	courseData := models.Course{
		Name:           "Go Programming",
		Description:    "Learn Go Programming language",
		Price:          199,
		Discount:       10.0,
		DurationInDays: 30,
		IsFree:         false,
		CourseType:     models.CourseTypeIITJEE,
		Subjects:       subjects,
	}

	rr := requestExecutionHelper(http.MethodPost, "/api/courses", courseData)
	// Check that the course was created
	assert.Equal(t, http.StatusOK, rr.Code)

	var createdCourse models.Course
	err := json.Unmarshal(rr.Body.Bytes(), &createdCourse)
	assert.Nil(t, err)
	assert.Equal(t, "Go Programming", createdCourse.Name)
	assert.Equal(t, "Learn Go Programming language", createdCourse.Description)
	return createdCourse
}

func RegisterStudent(t *testing.T) models.CreatedStudentResponse {
	user := map[string]interface{}{
		"full_name":       "John Doe",
		"email":           "<EMAIL>",
		"phone_number":    "1234567890",
		"password_hash":   "securepassword",
		"role":            "student",
		"contact_address": "123 Street",
	}
	studentPayload := map[string]interface{}{
		"user":         user,
		"parent_phone": "0987654321",
		"parent_email": "<EMAIL>",
	}
	resp := requestExecutionHelper(http.MethodPost, "/students", studentPayload)
	assert.Equal(t, http.StatusOK, resp.Code)
	var createdResp models.CreatedStudentResponse
	err := json.Unmarshal(resp.Body.Bytes(), &createdResp)
	assert.Nil(t, err)
	assert.Equal(t, "John Doe", createdResp.Student.Name)

	return createdResp
}

// CreateTestStudent creates a test student for transaction testing
func CreateTestStudent(t *testing.T) (models.Student, string) {
	// Generate unique identifiers to avoid conflicts
	timestamp := time.Now().UnixNano()
	uniqueEmail := fmt.Sprintf("<EMAIL>", timestamp)
	uniquePhone := fmt.Sprintf("123456%04d", timestamp%10000)
	uniqueParentPhone := fmt.Sprintf("098765%04d", timestamp%10000)
	uniqueParentEmail := fmt.Sprintf("<EMAIL>", timestamp)

	// Create user first
	user := models.UserForCreate{
		FullName:       "Test Student",
		Email:          uniqueEmail,
		PhoneNumber:    uniquePhone,
		ContactAddress: "Test Address",
	}

	// Create student
	student := models.StudentForCreate{
		UserForCreate: user,
		ParentPhone:   uniqueParentPhone,
		ParentEmail:   uniqueParentEmail,
	}

	resp := requestExecutionHelper("POST", "/api/students", student)
	if resp.Code != http.StatusOK {
		t.Logf("Student creation failed with status %d: %s", resp.Code, resp.Body.String())
	}
	assert.Equal(t, http.StatusOK, resp.Code)

	var createdResponse models.CreatedStudentResponse
	err := json.Unmarshal(resp.Body.Bytes(), &createdResponse)
	assert.Nil(t, err)

	// Fetch the full student from database since API only returns ID and name
	var fullStudent models.Student
	err = db.Preload("User").First(&fullStudent, createdResponse.Student.ID).Error
	assert.Nil(t, err)

	return fullStudent, createdResponse.Token
}

// CreateTestCourses creates test courses for transaction testing
func CreateTestCourses(t *testing.T) (models.Course, models.Course) {
	// Create a paid course
	paidCourse := models.CourseForCreate{
		Name:           "Test Paid Course",
		Description:    "A test paid course for transaction testing",
		Price:          1000,
		Discount:       10.0,
		DurationInDays: 30,
		IsFree:         false,
		CourseType:     models.CourseTypeIITJEE,
		SubjectNames:   []string{},
	}

	resp := requestExecutionHelper("POST", "/api/courses", paidCourse)
	assert.Equal(t, http.StatusOK, resp.Code)

	var createdPaidCourse models.Course
	err := json.Unmarshal(resp.Body.Bytes(), &createdPaidCourse)
	assert.Nil(t, err)

	// Create a free course
	freeCourse := models.CourseForCreate{
		Name:           "Test Free Course",
		Description:    "A test free course for transaction testing",
		Price:          0,
		Discount:       0.0,
		DurationInDays: 30,
		IsFree:         true,
		CourseType:     models.CourseTypeNEET,
		SubjectNames:   []string{},
	}

	resp = requestExecutionHelper("POST", "/api/courses", freeCourse)
	assert.Equal(t, http.StatusOK, resp.Code)

	var createdFreeCourse models.Course
	err = json.Unmarshal(resp.Body.Bytes(), &createdFreeCourse)
	assert.Nil(t, err)

	return createdPaidCourse, createdFreeCourse
}

// CleanupTransactionTestData cleans up transaction-related test data
func CleanupTransactionTestData() {
	// Clean up in order to respect foreign key constraints
	db.Exec("DELETE FROM transaction_courses WHERE transaction_id IN (SELECT id FROM transactions WHERE student_id IN (SELECT id FROM students WHERE user_id IN (SELECT id FROM users WHERE email LIKE '%@example.com')))")
	db.Exec("DELETE FROM transactions WHERE student_id IN (SELECT id FROM students WHERE user_id IN (SELECT id FROM users WHERE email LIKE '%@example.com'))")
	db.Exec("DELETE FROM students_courses WHERE student_id IN (SELECT id FROM students WHERE user_id IN (SELECT id FROM users WHERE email LIKE '%@example.com'))")
	db.Exec("DELETE FROM students WHERE user_id IN (SELECT id FROM users WHERE email LIKE '%@example.com')")
	db.Exec("DELETE FROM courses WHERE name LIKE 'Test %Course'")
	db.Exec("DELETE FROM users WHERE email LIKE '%@example.com'")
}
