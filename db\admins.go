package db

import (
	"context"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"golang.org/x/crypto/bcrypt"
)

func (p *DbPlugin) CreateAdmin(ctx context.Context, admin *models.User) (*models.User, error) {
	start := time.Now()
	slog.Info("Creating admin user", "email", admin.Email, "full_name", admin.FullName)

	// Hash the password before storing
	if admin.PasswordHash != "" {
		combined := admin.PasswordHash + token.SecretKeyStr
		hash, err := bcrypt.GenerateFromPassword([]byte(combined), bcrypt.DefaultCost)
		if err != nil {
			duration := time.Since(start)
			slog.Error("Failed to hash admin password",
				"email", admin.Email,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, err
		}
		admin.PasswordHash = string(hash)
	}

	// Ensure the role is set to Admin
	admin.Role = "Admin"

	// Sanitize the user data
	admin.Sanitize()

	slog.Debug("Creating admin with sanitized data",
		"email", admin.Email,
		"full_name", admin.FullName,
		"phone_number", admin.PhoneNumber,
	)

	// Create the admin user in the database
	res := p.db.Create(admin)
	duration := time.Since(start)

	if res.Error != nil {
		slog.Error("Failed to create admin user",
			"email", admin.Email,
			"full_name", admin.FullName,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	// Clear the password hash from the returned object for security
	admin.PasswordHash = ""

	slog.Info("Admin user created successfully",
		"admin_id", admin.ID,
		"email", admin.Email,
		"full_name", admin.FullName,
		"duration_ms", duration.Milliseconds(),
	)
	return admin, nil
}
