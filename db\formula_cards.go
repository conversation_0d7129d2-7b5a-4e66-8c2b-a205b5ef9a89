package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

// CreateFormulaCards creates multiple formula cards for a subject in a single transaction
func (p *DbPlugin) CreateFormulaCards(ctx context.Context, formulaCardsInput *models.FormulaCardsForCreate) ([]models.FormulaCard, error) {
	start := time.Now()
	slog.Info("Creating formula cards",
		"subject_name", formulaCardsInput.SubjectName,
		"card_count", len(formulaCardsInput.FormulaCards),
	)

	// Validate input before starting transaction
	if len(formulaCardsInput.FormulaCards) == 0 {
		return nil, fmt.Errorf("no formula cards provided")
	}

	// Find the subject by name before starting transaction
	var subject models.Subject
	if err := p.db.Where("name = ?", formulaCardsInput.SubjectName).First(&subject).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Subject not found for formula cards creation",
			"subject_name", formulaCardsInput.SubjectName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("subject '%s' not found: %w", formulaCardsInput.SubjectName, err)
	}

	// Start transaction for creating formula cards
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			slog.Error("Panic during formula cards creation",
				"subject_name", formulaCardsInput.SubjectName,
				"panic", r,
			)
			tx.Rollback()
		}
	}()

	var createdCards []models.FormulaCard

	// Create each formula card
	for _, cardInput := range formulaCardsInput.FormulaCards {
		formulaCard := models.FormulaCard{
			Name:      cardInput.Name,
			ImageUrl:  cardInput.ImageUrl,
			SubjectID: subject.ID,
		}

		if err := tx.Create(&formulaCard).Error; err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to create formula card",
				"subject_name", formulaCardsInput.SubjectName,
				"card_name", cardInput.Name,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to create formula card '%s': %w", cardInput.Name, err)
		}

		// Load the formula card with subject association
		if err := tx.Preload("Subject").First(&formulaCard, formulaCard.ID).Error; err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to load formula card with subject association",
				"card_id", formulaCard.ID,
				"card_name", cardInput.Name,
				"subject_name", formulaCardsInput.SubjectName,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to load formula card with subject: %w", err)
		}

		createdCards = append(createdCards, formulaCard)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit formula cards creation transaction",
			"subject_name", formulaCardsInput.SubjectName,
			"card_count", len(formulaCardsInput.FormulaCards),
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Formula cards created successfully",
		"subject_name", formulaCardsInput.SubjectName,
		"subject_id", subject.ID,
		"card_count", len(createdCards),
		"duration_ms", duration.Milliseconds(),
	)

	return createdCards, nil
}

// GetAllFormulaCardsOrganizedBySubject retrieves all formula cards organized by subject
func (p *DbPlugin) GetAllFormulaCardsOrganizedBySubject(ctx context.Context) ([]models.FormulaCardsBySubject, error) {
	start := time.Now()
	slog.Debug("Retrieving all formula cards organized by subject")

	var formulaCards []models.FormulaCard

	// Get all formula cards with their subjects
	err := p.db.Preload("Subject").Find(&formulaCards).Error

	duration := time.Since(start)

	if err != nil {
		slog.Error("Failed to retrieve all formula cards",
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve formula cards: %w", err)
	}

	// Organize formula cards by subject
	subjectMap := make(map[string][]models.FormulaCardSummary)

	for _, card := range formulaCards {
		subjectName := card.Subject.Name

		cardSummary := models.FormulaCardSummary{
			ID:          card.ID,
			Name:        card.Name,
			SubjectName: subjectName,
			ImageUrl:    card.ImageUrl,
		}

		subjectMap[subjectName] = append(subjectMap[subjectName], cardSummary)
	}

	// Convert map to slice
	var result []models.FormulaCardsBySubject
	for subjectName, cards := range subjectMap {
		result = append(result, models.FormulaCardsBySubject{
			SubjectName:  subjectName,
			FormulaCards: cards,
		})
	}

	slog.Debug("Formula cards organized by subject retrieved successfully",
		"subject_count", len(result),
		"total_card_count", len(formulaCards),
		"duration_ms", duration.Milliseconds(),
	)

	return result, nil
}
