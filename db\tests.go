package db

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"

	"gorm.io/gorm"
)

func (p *DbPlugin) CreateSectionType(ctx context.Context, sectionType *models.SectionType, subjectName string) (*models.SectionType, error) {
	// Find the subject by name
	var subject models.Subject
	if err := p.db.Where("name = ?", subjectName).First(&subject).Error; err != nil {
		return nil, fmt.Errorf("subject '%s' not found: %w", subjectName, err)
	}

	// Set the subject ID
	sectionType.SubjectID = subject.ID

	res := p.db.Create(sectionType)
	if res.Error != nil {
		return nil, res.Error
	}

	// Load the section type with subject association
	if err := p.db.Preload("Subject").First(sectionType, sectionType.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to load section type with subject: %w", err)
	}

	return sectionType, nil
}

func (p *DbPlugin) CreateTestType(ctx context.Context, testType *models.TestType, sectionTypeNames []string) (*models.TestType, error) {
	// First, find the section types by their names
	var sectionTypes []models.SectionType
	if len(sectionTypeNames) > 0 {
		// Find existing section types
		if err := p.db.Where("name IN ?", sectionTypeNames).Find(&sectionTypes).Error; err != nil {
			return nil, fmt.Errorf("failed to find section types: %w", err)
		}

		// Check if all section types were found
		if len(sectionTypes) != len(sectionTypeNames) {
			return nil, errors.New("one or more section types not found")
		}
	}

	// Create the test type
	testTypeToCreate := &models.TestType{
		Name: testType.Name,
	}

	res := p.db.Create(testTypeToCreate)
	if res.Error != nil {
		return nil, res.Error
	}

	// Associate section types with the test type
	if len(sectionTypes) > 0 {
		if err := p.db.Model(testTypeToCreate).Association("SectionTypes").Append(sectionTypes); err != nil {
			return nil, fmt.Errorf("failed to associate section types: %w", err)
		}
	}

	// Load the associations for the response
	if err := p.db.Preload("SectionTypes").First(testTypeToCreate, testTypeToCreate.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to load test type with associations: %w", err)
	}

	return testTypeToCreate, nil
}

func (p *DbPlugin) CreateTest(ctx context.Context, test *models.Test, testTypeName string) (*models.Test, error) {
	// Start a transaction
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Find the test type by name with its associated section types
	var testType models.TestType
	if err := tx.Preload("SectionTypes").Where("name = ?", testTypeName).First(&testType).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("test type '%s' not found: %w", testTypeName, err)
	}

	// Create the test first
	testToCreate := &models.Test{
		Name:             test.Name,
		TestTypeID:       testType.ID,
		FromTime:         test.FromTime,
		ToTime:           test.ToTime,
		Active:           false, // New tests are always inactive by default
		Description:      test.Description,
		EvaluationStatus: models.EvaluationStatusNotApplicable,
	}

	if err := tx.Create(testToCreate).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create test: %w", err)
	}

	// Create sections automatically based on test type's section types
	sectionLabels := []string{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J"}
	for i, sectionType := range testType.SectionTypes {
		// Generate unique section name and display name
		sectionName := fmt.Sprintf("%s_section_%s_%d", testToCreate.Name, sectionLabels[i%len(sectionLabels)], testToCreate.ID)
		displayName := fmt.Sprintf("Section %s", sectionLabels[i%len(sectionLabels)])

		// Create the section
		section := &models.Section{
			Name:          sectionName,
			DisplayName:   displayName,
			TestID:        testToCreate.ID,
			SectionTypeID: sectionType.ID,
		}

		if err := tx.Create(section).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to create section '%s': %w", sectionName, err)
		}
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Load the test with its sections, section types, and test type
	if err := p.db.Preload("TestType").Preload("Sections.SectionType").First(testToCreate, testToCreate.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to load test with associations: %w", err)
	}

	return testToCreate, nil
}

func (p *DbPlugin) AddQuestionsToTest(ctx context.Context, testID uint, questionIDs []uint, sectionName string) error {
	// Start a transaction
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Verify the test exists
	var test models.Test
	if err := tx.First(&test, testID).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("test with ID %d not found", testID)
		}
		return fmt.Errorf("failed to find test: %w", err)
	}

	// Verify all questions exist
	var questions []models.Question
	if err := tx.Where("id IN ?", questionIDs).Find(&questions).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to find questions: %w", err)
	}

	if len(questions) != len(questionIDs) {
		tx.Rollback()
		return errors.New("one or more questions not found")
	}

	// Find the specific section by name within the test
	var section models.Section
	if err := tx.Preload("SectionType").Where("test_id = ? AND name = ?", testID, sectionName).First(&section).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("section '%s' not found in test with ID %d", sectionName, testID)
		}
		return fmt.Errorf("failed to find section: %w", err)
	}

	// Add questions to the section using GORM's many-to-many association
	if err := tx.Model(&section).Association("Questions").Append(questions); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to add questions to section: %w", err)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

func (p *DbPlugin) GetTests(ctx context.Context, userID uint, activeOnly *bool) ([]models.TestForGet, error) {
	start := time.Now()
	tests := make([]models.Test, 0)

	slog.Debug("GetTests database operation started",
		"user_id", userID,
		"active_only", activeOnly,
	)

	// First, get the user to determine their role
	var user models.User
	if err := p.db.Where("id = ?", userID).First(&user).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to find user for GetTests",
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to find user: %w", err)
	}

	slog.Debug("User found for GetTests",
		"user_id", userID,
		"role", user.Role,
		"email", user.Email,
	)

	// Build the base query with preloaded associations
	query := p.db.Preload("TestType").Preload("Sections")

	// Apply active filter if specified
	if activeOnly != nil {
		query = query.Where("active = ?", *activeOnly)
		slog.Debug("Applied active filter",
			"user_id", userID,
			"active_only", *activeOnly,
		)
	}

	// If user is a student, filter tests by enrolled courses but always include ZSAT tests
	if user.Role == "Student" {
		slog.Debug("Processing student test access", "user_id", userID)

		// Get the student record
		var student models.Student
		if err := p.db.Preload("Courses").Where("user_id = ?", userID).First(&student).Error; err != nil {
			duration := time.Since(start)
			slog.Error("Failed to find student for GetTests",
				"user_id", userID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to find student: %w", err)
		}

		// Extract course IDs
		var courseIDs []uint
		for _, course := range student.Courses {
			courseIDs = append(courseIDs, course.ID)
		}

		slog.Debug("Student enrolled courses found",
			"user_id", userID,
			"course_count", len(student.Courses),
			"course_ids", courseIDs,
		)

		// Build the query to include:
		// 1. Tests from enrolled courses
		// 2. All ZSAT type tests (regardless of course enrollment)
		if len(courseIDs) > 0 {
			// Student has enrolled courses - include both enrolled course tests and ZSAT tests
			query = query.Where(
				"tests.id IN (SELECT test_id FROM courses_tests WHERE course_id IN ?) OR tests.test_type_id IN (SELECT id FROM test_types WHERE name = ?)",
				courseIDs, "ZSAT",
			)
		} else {
			// Student has no enrolled courses - only include ZSAT tests
			query = query.Where("tests.test_type_id IN (SELECT id FROM test_types WHERE name = ?)", "ZSAT")
		}

		slog.Debug("Applied student test filtering with ZSAT inclusion",
			"user_id", userID,
			"enrolled_course_count", len(courseIDs),
		)
	} else {
		slog.Debug("Admin user - no course filtering applied", "user_id", userID)
	}

	// Execute the query
	if err := query.Find(&tests).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve tests from database",
			"user_id", userID,
			"role", user.Role,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve tests: %w", err)
	}

	duration := time.Since(start)
	slog.Info("GetTests database operation completed",
		"user_id", userID,
		"role", user.Role,
		"test_count", len(tests),
		"active_filter", activeOnly,
		"duration_ms", duration.Milliseconds(),
	)
	var testsForGet []models.TestForGet
	for _, test := range tests {
		var sectionNames []string
		for _, section := range test.Sections {
			sectionNames = append(sectionNames, section.Name)
		}
		testsForGet = append(testsForGet, models.TestForGet{
			ID:               test.ID,
			Name:             test.Name,
			TestType:         test.TestType.Name,
			SectionNames:     sectionNames,
			FromTime:         test.FromTime,
			ToTime:           test.ToTime,
			Active:           test.Active,
			EvaluationStatus: test.EvaluationStatus,
			Description:      test.Description,
		})
	}

	return testsForGet, nil
}

// GetTestTypes retrieves all test types with their associated section types
func (p *DbPlugin) GetTestTypes(ctx context.Context) ([]models.TestType, error) {
	start := time.Now()
	slog.Info("Retrieving all test types")

	var testTypes []models.TestType

	// Retrieve test types with preloaded section types and their subjects
	if err := p.db.Preload("SectionTypes.Subject").Find(&testTypes).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve test types",
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	duration := time.Since(start)
	slog.Info("Test types retrieved successfully",
		"test_type_count", len(testTypes),
		"duration_ms", duration.Milliseconds(),
	)

	return testTypes, nil
}

// GetSectionTypes retrieves all section types with their associated subjects
func (p *DbPlugin) GetSectionTypes(ctx context.Context) ([]models.SectionType, error) {
	start := time.Now()
	slog.Info("Retrieving all section types")

	var sectionTypes []models.SectionType

	// Retrieve section types with preloaded subjects
	if err := p.db.Preload("Subject").Find(&sectionTypes).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve section types",
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	duration := time.Since(start)
	slog.Info("Section types retrieved successfully",
		"section_type_count", len(sectionTypes),
		"duration_ms", duration.Milliseconds(),
	)

	return sectionTypes, nil
}

// RemoveQuestionsFromTest removes multiple questions from a specific section of a test
func (p *DbPlugin) RemoveQuestionsFromTest(ctx context.Context, testID uint, questionIDs []uint, sectionName string) error {
	start := time.Now()
	slog.Info("Removing questions from test",
		"test_id", testID,
		"section_name", sectionName,
		"question_ids", questionIDs,
		"question_count", len(questionIDs),
	)

	// Validation: ensure we have questions to remove
	if len(questionIDs) == 0 {
		duration := time.Since(start)
		slog.Warn("No question IDs provided for removal",
			"test_id", testID,
			"section_name", sectionName,
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("no question IDs provided for removal")
	}

	// Start a transaction
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Verify the test exists
	var test models.Test
	if err := tx.First(&test, testID).Error; err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Test not found for question removal",
			"test_id", testID,
			"section_name", sectionName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("test with ID %d not found", testID)
		}
		return fmt.Errorf("failed to find test: %w", err)
	}

	// Find the specific section by name within the test
	var section models.Section
	if err := tx.Preload("SectionType").Where("test_id = ? AND name = ?", testID, sectionName).First(&section).Error; err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Section not found for question removal",
			"test_id", testID,
			"section_name", sectionName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("section '%s' not found in test with ID %d", sectionName, testID)
		}
		return fmt.Errorf("failed to find section: %w", err)
	}

	// Verify all questions exist and are currently associated with the section
	var existingQuestions []models.Question
	if err := tx.Model(&section).Association("Questions").Find(&existingQuestions, "questions.id IN ?", questionIDs); err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to find questions in section",
			"test_id", testID,
			"section_name", sectionName,
			"question_ids", questionIDs,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("failed to find questions in section: %w", err)
	}

	// Check if all requested questions are actually in the section
	if len(existingQuestions) != len(questionIDs) {
		tx.Rollback()
		duration := time.Since(start)

		// Find which questions are missing
		existingIDs := make(map[uint]bool)
		for _, q := range existingQuestions {
			existingIDs[q.ID] = true
		}

		var missingIDs []uint
		for _, id := range questionIDs {
			if !existingIDs[id] {
				missingIDs = append(missingIDs, id)
			}
		}

		slog.Warn("Some questions not found in section",
			"test_id", testID,
			"section_name", sectionName,
			"requested_questions", questionIDs,
			"found_questions", len(existingQuestions),
			"missing_questions", missingIDs,
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("some questions are not associated with section '%s': missing question IDs %v", sectionName, missingIDs)
	}

	// Remove questions from the section using GORM's many-to-many association
	if err := tx.Model(&section).Association("Questions").Delete(existingQuestions); err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to remove questions from section",
			"test_id", testID,
			"section_name", sectionName,
			"question_ids", questionIDs,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("failed to remove questions from section: %w", err)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit question removal transaction",
			"test_id", testID,
			"section_name", sectionName,
			"question_ids", questionIDs,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Questions removed from test successfully",
		"test_id", testID,
		"section_name", sectionName,
		"question_ids", questionIDs,
		"question_count", len(questionIDs),
		"duration_ms", duration.Milliseconds(),
	)

	return nil
}

// ToggleTestActiveStatus toggles the active status of a test
func (p *DbPlugin) ToggleTestActiveStatus(ctx context.Context, testID uint) error {
	start := time.Now()
	slog.Info("Toggling test active status",
		"test_id", testID,
	)

	// Start a transaction
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Verify the test exists
	var test models.Test
	if err := tx.First(&test, testID).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("test with ID %d not found", testID)
		}
		return fmt.Errorf("failed to find test: %w", err)
	}

	// Toggle the active status
	test.Active = !test.Active
	if err := tx.Save(&test).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update test: %w", err)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Test active status toggled successfully",
		"test_id", testID,
		"active", test.Active,
		"duration_ms", duration.Milliseconds(),
	)

	return nil
}

// UpdateTestEvaluationStatus updates the evaluation status of a test
func (p *DbPlugin) UpdateTestEvaluationStatus(ctx context.Context, testID uint, status models.EvaluationStatus) error {
	start := time.Now()
	slog.Info("Updating test evaluation status",
		"test_id", testID,
		"status", status,
	)

	// Update the test's evaluation status
	result := p.db.Model(&models.Test{}).Where("id = ?", testID).Update("evaluation_status", status)
	if result.Error != nil {
		duration := time.Since(start)
		slog.Error("Failed to update test evaluation status",
			"test_id", testID,
			"status", status,
			"error", result.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return result.Error
	}

	if result.RowsAffected == 0 {
		duration := time.Since(start)
		slog.Warn("Test not found for evaluation status update",
			"test_id", testID,
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("test with ID %d not found", testID)
	}

	duration := time.Since(start)
	slog.Info("Test evaluation status updated successfully",
		"test_id", testID,
		"status", status,
		"duration_ms", duration.Milliseconds(),
	)

	return nil
}

// DetermineTestEvaluationStatus determines the evaluation status of a test based on its responses
func (p *DbPlugin) DetermineTestEvaluationStatus(ctx context.Context, testID uint) (models.EvaluationStatus, error) {
	// Count total responses for the test
	var totalResponses int64
	if err := p.db.Model(&models.TestResponse{}).Where("test_id = ?", testID).Count(&totalResponses).Error; err != nil {
		return models.EvaluationStatusNotApplicable, fmt.Errorf("failed to count total responses: %w", err)
	}

	// If no responses, status is NotApplicable
	if totalResponses == 0 {
		return models.EvaluationStatusNotApplicable, nil
	}

	// Count unevaluated responses (where calculated_score is NULL)
	var unevaluatedResponses int64
	if err := p.db.Model(&models.TestResponse{}).Where("test_id = ? AND calculated_score IS NULL", testID).Count(&unevaluatedResponses).Error; err != nil {
		return models.EvaluationStatusPending, fmt.Errorf("failed to count unevaluated responses: %w", err)
	}

	// If there are unevaluated responses, status is Pending
	if unevaluatedResponses > 0 {
		return models.EvaluationStatusPending, nil
	}

	// All responses are evaluated
	return models.EvaluationStatusEvaluated, nil
}
