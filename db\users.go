package db

import (
	"context"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"golang.org/x/crypto/bcrypt"
)

func (p *DbPlugin) ValidateUserPassword(ctx context.Context, userEmail, pwd string) (uint, error) {
	start := time.Now()
	slog.Debug("Validating user password", "email", userEmail)

	var user models.User
	result := p.db.Where("email = ?", userEmail).First(&user)
	duration := time.Since(start)

	if result.Error != nil {
		slog.Warn("User not found during password validation",
			"email", userEmail,
			"error", result.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return 0, result.Error
	}

	combined := pwd + token.SecretKeyStr
	err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(combined))
	duration = time.Since(start)

	if err != nil {
		slog.Warn("Password validation failed",
			"email", userEmail,
			"user_id", user.ID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return 0, err
	}

	slog.Debug("Password validation successful",
		"email", userEmail,
		"user_id", user.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return user.ID, nil
}

func (p *DbPlugin) UpdatePassword(ctx context.Context, userID uint,
	newPwd string) error {
	start := time.Now()
	slog.Info("Updating user password", "user_id", userID)

	combined := newPwd + token.SecretKeyStr
	hash, err := bcrypt.GenerateFromPassword([]byte(combined), bcrypt.DefaultCost)
	if err != nil {
		duration := time.Since(start)
		slog.Error("Failed to generate password hash",
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return err
	}

	var user models.User
	result := p.db.First(&user, userID)
	if result.Error != nil {
		duration := time.Since(start)
		slog.Error("Failed to find user for password update",
			"user_id", userID,
			"error", result.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return result.Error
	}

	user.PasswordHash = string(hash)
	result = p.db.Save(&user)
	duration := time.Since(start)

	if result.Error != nil {
		slog.Error("Failed to save updated password",
			"user_id", userID,
			"email", user.Email,
			"error", result.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return result.Error
	}

	slog.Info("Password updated successfully",
		"user_id", userID,
		"email", user.Email,
		"duration_ms", duration.Milliseconds(),
	)
	return nil
}
