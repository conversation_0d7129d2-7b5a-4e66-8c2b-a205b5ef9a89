package middleware

import (
	"log/slog"
	"net/http"
	"time"
	"ziaacademy-backend/internal/token"

	"github.com/gin-gonic/gin"
)

func JwtAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		clientIP := c.ClientIP()
		method := c.Request.Method
		path := c.Request.URL.Path

		slog.Debug("Authentication attempt",
			"method", method,
			"path", path,
			"client_ip", clientIP,
		)

		err := token.TokenValid(c)
		if err != nil {
			duration := time.Since(start)
			slog.Warn("Authentication failed",
				"method", method,
				"path", path,
				"client_ip", clientIP,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			c.String(http.StatusUnauthorized, "Unauthorized")
			c.Abort()
			return
		}

		duration := time.Since(start)
		slog.Debug("Authentication successful",
			"method", method,
			"path", path,
			"client_ip", clientIP,
			"duration_ms", duration.Milliseconds(),
		)
		c.Next()
	}
}

// RequestLoggingMiddleware logs all HTTP requests with timing and response status
func RequestLoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method
		clientIP := c.ClientIP()
		userAgent := c.Request.UserAgent()

		slog.Info("Request started",
			"method", method,
			"path", path,
			"client_ip", clientIP,
			"user_agent", userAgent,
		)

		// Process request
		c.Next()

		// Log response
		duration := time.Since(start)
		statusCode := c.Writer.Status()
		responseSize := c.Writer.Size()

		logLevel := slog.LevelInfo
		if statusCode >= 400 && statusCode < 500 {
			logLevel = slog.LevelWarn
		} else if statusCode >= 500 {
			logLevel = slog.LevelError
		}

		slog.Log(c.Request.Context(), logLevel, "Request completed",
			"method", method,
			"path", path,
			"client_ip", clientIP,
			"status_code", statusCode,
			"duration_ms", duration.Milliseconds(),
			"response_size", responseSize,
		)
	}
}
