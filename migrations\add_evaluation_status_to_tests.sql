-- Migration to add evaluation_status column to tests table
-- This column tracks whether a test has been attempted, has pending evaluations, or is fully evaluated

-- Add the evaluation_status column with default value
ALTER TABLE tests ADD COLUMN evaluation_status VARCHAR(20) DEFAULT 'NotApplicable';

-- Add a check constraint to ensure only valid values are allowed
ALTER TABLE tests ADD CONSTRAINT check_evaluation_status 
    CHECK (evaluation_status IN ('NotApplicable', 'Pending', 'Evaluated'));

-- Create an index on evaluation_status for better query performance
CREATE INDEX idx_tests_evaluation_status ON tests(evaluation_status);

-- Update existing tests to have the correct evaluation status based on their current state
-- This will set the status based on whether they have responses and if those responses are evaluated

-- First, set all tests with no responses to 'NotApplicable' (this is already the default)
UPDATE tests 
SET evaluation_status = 'NotApplicable' 
WHERE id NOT IN (
    SELECT DISTINCT test_id 
    FROM test_responses
);

-- Set tests with unevaluated responses to 'Pending'
UPDATE tests 
SET evaluation_status = 'Pending' 
WHERE id IN (
    SELECT DISTINCT test_id 
    FROM test_responses 
    WHERE calculated_score IS NULL
);

-- Set tests where all responses are evaluated to 'Evaluated'
UPDATE tests 
SET evaluation_status = 'Evaluated' 
WHERE id IN (
    SELECT test_id 
    FROM test_responses 
    GROUP BY test_id 
    HAVING COUNT(*) > 0 AND COUNT(calculated_score) = COUNT(*)
);
